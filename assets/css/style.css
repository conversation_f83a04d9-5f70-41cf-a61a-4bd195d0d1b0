/*
 * style.css
 * Основные кастомные стили проекта
 */

/* --- Hero Section --- */
#hero {
    /* Фон с затемняющим оверлеем для читаемости текста */
    background-image: 
        linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.6)), 
        url('../images/hero-background.jpg'); /* Убедись, что картинка лежит здесь */
    background-size: cover;
    background-position: center;
    
    /* Расположение контента */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    min-height: 80vh; /* Занимает большую часть экрана */
    padding: 2rem 1rem;
    text-align: center;
}

#hero hgroup {
    margin-bottom: 2rem;
}

#hero h1, #hero h2 {
    color: var(--pico-primary-inverse); /* Белый цвет */
}

#hero h1 {
    font-size: 2.5rem;
}

#hero h2 {
    font-size: 1.2rem;
    font-weight: 400; /* Обычная жирность для подзаголовка */
    max-width: 60ch; /* Ограничиваем длину строки для лучшей читаемости */
    margin: 1rem auto 0;
}

/* Стили для кнопки в Hero */
#hero button {
    --pico-font-size: 1.1rem;
    --pico-padding: 0.75rem 1.5rem;
}

/* Адаптивность для больших экранов */
@media (min-width: 768px) {
    #hero h1 {
        font-size: 3.5rem;
    }
    #hero h2 {
        font-size: 1.5rem;
    }
}

/* --- Header --- */
body > header {
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.8); /* Полупрозрачный фон для светлой темы */
    border-bottom: 1px solid var(--pico-muted-border-color);
}

header .brand {
    font-size: 1.2rem;
    text-decoration: none;
    color: var(--pico-h1-color);
}

/* --- Modal --- */
#contact-modal .contact-options {
    display: grid;
    gap: 1rem;
    margin-top: 1.5rem;
}

#contact-modal .contact-options a {
    text-align: center;
}

/* --- Section: Category (NEW) --- */
.category-section {
    padding-top: 4rem;
    padding-bottom: 4rem;
    border-bottom: 1px solid var(--pico-muted-border-color);
}

.category-section:nth-of-type(2n) { /* Чередуем фон для лучшего разделения */
    background-color: var(--pico-muted-background-color);
}

.category-section-header {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.category-section-header hgroup {
    margin-bottom: 0;
}

.category-section-header h2 {
    margin-bottom: 0.5rem;
}

.category-section-header p {
    font-size: 1.1rem;
    color: var(--pico-secondary);
    margin: 0;
}

.category-meta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.price-tag {
    font-size: 1.2rem;
    font-weight: bold;
    padding: 0.5rem 1rem;
    background-color: var(--pico-primary-background);
    color: var(--pico-primary);
    border-radius: var(--pico-border-radius);
    text-align: center;
}

/* Адаптивность для хедера секции */
@media (min-width: 768px) {
    .category-section-header {
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
    }
    .category-meta {
        flex-direction: column;
        align-items: flex-end;
        min-width: 220px;
        text-align: right;
    }
    .price-tag {
        width: 100%;
    }
}

/* Стили для слайдера в секции (используем стили от social-proof) */
.category-section .slide-item {
    flex: 0 0 100%;
    padding: 0 0.5rem;
    box-sizing: border-box;
}
.category-section .aspect-4-3 { aspect-ratio: 4 / 3; }

.slide-link {
    display: block;
    position: relative;
    border-radius: var(--pico-border-radius);
    overflow: hidden;
    background-color: var(--pico-card-background-color);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.slide-link img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.slide-link:hover img {
    transform: scale(1.05);
}

/* Иконка зума */
.zoom-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    width: 4rem;
    height: 4rem;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    opacity: 0;
}
.zoom-icon::after {
    content: '⚲'; /* Символ лупы */
    color: white;
    font-size: 2rem;
}
.slide-link:hover .zoom-icon {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

/* Адаптивность для слайдера категорий */
@media (min-width: 768px) {
    .category-section .slide-item { flex-basis: 50%; }
}
@media (min-width: 992px) {
    .category-section .slide-item { flex-basis: 33.333%; }
}
@media (min-width: 1200px) {
    .category-section .slide-item { flex-basis: 25%; }
}


/* --- Lightbox --- */
.lightbox-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.lightbox-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(8px);
    cursor: pointer;
}

.lightbox-content {
    position: relative;
    z-index: 1;
    max-width: 90vw;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.lightbox-content .close-button {
    position: absolute;
    top: -2.5rem;
    right: -1rem;
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    border-radius: 50%;
    border: none;
    background: none;
    color: white;
    font-size: 2.5rem;
    line-height: 1;
    cursor: pointer;
    opacity: 0.8;
}
.lightbox-content .close-button:hover {
    opacity: 1;
}

.lightbox-content .image-container {
    display: flex;
    align-items: center;
    justify-content: center;
}

.lightbox-content img {
    display: block;
    max-width: 100%;
    max-height: calc(90vh - 5rem); /* Оставляем место для описания */
    object-fit: contain;
    border-radius: var(--pico-border-radius);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.alt-text-container {
    text-align: center;
    color: var(--pico-primary-inverse);
}
.alt-text-container p {
    margin: 0;
}

/* Стили для слайдера */
.slider {
    position: relative;
    overflow: hidden;
    aspect-ratio: 4 / 3; /* Сохраняем пропорции, чтобы карточки не прыгали */
    background-color: var(--pico-muted-background-color);
    border-radius: var(--pico-border-radius);
    margin-top: 1rem;
}

.slider-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.slider-wrapper img {
    position: absolute; /* Накладываем картинки друг на друга */
    width: 100%;
    height: 100%;
    object-fit: cover; /* Масштабируем картинку, чтобы она заполнила контейнер */
}

/* Кнопки навигации слайдера */
.slider-nav {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 0.5rem;
    pointer-events: none; /* Позволяет кликать сквозь контейнер */
}

.slider-nav button {
    pointer-events: all; /* Делаем кнопки кликабельными */
    background-color: rgba(20, 23, 26, 0.6);
    color: white;
    border: none;
    border-radius: 50%;
    width: 2.5rem;
    height: 2.5rem;
    line-height: 2.5rem;
    padding: 0;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
}

.slider:hover .slider-nav button {
    opacity: 1; /* Показываем кнопки при наведении на слайдер */
}

/* Точки-индикаторы */
.slider-dots {
    position: absolute;
    bottom: 0.75rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
}

.slider-dots button {
    width: 0.75rem;
    height: 0.75rem;
    padding: 0;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.7);
    background-color: transparent;
    cursor: pointer;
}

.slider-dots button.active {
    background-color: white;
}

.category-card footer {
    padding-top: 1rem;
}
.category-card footer button {
    width: 100%;
}

/* --- Section: Social Proof --- */
#social-proof {
    padding-top: 4rem;
    padding-bottom: 4rem;
    background-color: var(--pico-muted-background-color);
}

#social-proof hgroup {
    text-align: center;
    margin-bottom: 3rem;
}

.social-gallery {
    margin-bottom: 3rem;
}

.social-gallery h3 {
    margin-bottom: 1.5rem;
    text-align: center;
}

.slider-multi-item {
    position: relative;
}

.slider-container {
    overflow: hidden; /* Главный трюк: обрезаем все, что не помещается */
}

.slider-wrapper-flex {
    display: flex;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* Плавный переход */
}

.slide-item {
    flex: 0 0 100%; /* На мобильных один слайд занимает 100% ширины */
    padding: 0 0.5rem;
    box-sizing: border-box;
}

.slide-item a {
    display: block;
    position: relative;
    border-radius: var(--pico-border-radius);
    overflow: hidden;
    background-color: var(--pico-card-background-color);
}

.slide-item img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.slide-item a:hover img {
    transform: scale(1.05);
}

/* Пропорции для превью */
.aspect-16-9 { aspect-ratio: 16 / 9; }
.aspect-9-16 { aspect-ratio: 9 / 16; }

/* Иконка Play */
.play-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 4rem;
    height: 4rem;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}
.play-icon::after {
    content: '';
    display: block;
    width: 0;
    height: 0;
    border-top: 0.7rem solid transparent;
    border-bottom: 0.7rem solid transparent;
    border-left: 1.2rem solid white;
    margin-left: 0.3rem;
}
.slide-item a:hover .play-icon {
    background-color: rgba(var(--pico-primary-rgb), 0.8);
}


/* Внешние кнопки навигации */
.slider-nav-external {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1.5rem;
}

.slider-nav-external button {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
}

.slider-nav-external button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Адаптивность для планшетов и десктопов */
@media (min-width: 768px) {
    .slide-item { flex-basis: 50%; } /* 2 слайда */
}
@media (min-width: 1024px) {
    .slide-item { flex-basis: 33.333%; } /* 3 слайда */
}

/* --- Section: FAQ --- */
#faq {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

#faq hgroup {
    text-align: center;
    margin-bottom: 3rem;
}

.faq-accordion details {
    border-bottom: 1px solid var(--pico-muted-border-color);
    margin-bottom: 0.5rem;
}

.faq-accordion details[open] {
    padding-bottom: 1rem;
}

.faq-accordion summary {
    font-weight: bold;
    cursor: pointer;
    padding: 1rem 0;
}

/* Убираем стандартный маркер и добавляем свой через псевдоэлемент */
.faq-accordion summary {
    list-style: none; /* Убрать стандартный треугольник в Chrome/Safari */
}
.faq-accordion summary::-webkit-details-marker {
    display: none; /* И еще раз для Safari */
}
.faq-accordion summary::after {
    content: '+';
    float: right;
    font-size: 1.5rem;
    font-weight: 400;
    transition: transform 0.2s ease-in-out;
}

.faq-accordion details[open] summary::after {
    transform: rotate(45deg);
    content: '×'; /* Меняем плюсик на крестик */
}

.faq-accordion details p {
    margin-top: 0.5rem;
    padding-left: 0.5rem;
    line-height: 1.7;
}

/* --- Section: Map --- */
#map {
    position: relative;
    padding: 4rem 1rem;
    background-image: 
        linear-gradient(to right, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.1) 60%),
        url('../images/map-background.jpg'); /* Убедись, что картинка лежит здесь */
    background-size: cover;
    background-position: center;
}

.map-info {
    max-width: 450px; /* Ограничиваем ширину инфоблока */
    padding: 2rem;
    border-radius: var(--pico-border-radius);
    background-color: var(--pico-card-background-color);
}

.map-info p {
    margin-bottom: 1.5rem;
}

.map-info footer a {
    width: 100%;
}

/* На мобильных устройствах инфоблок по центру */
@media (max-width: 767px) {
    #map {
        background-image: 
            linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
            url('../images/map-background.jpg');
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .map-info {
        width: 100%;
        text-align: center;
    }
}

/* --- Footer --- */
#main-footer {
    padding: 2rem 0;
    background-color: var(--pico-muted-background-color);
    border-top: 1px solid var(--pico-muted-border-color);
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    text-align: center;
}

.social-links ul {
    display: flex;
    gap: 1.5rem;
    padding: 0;
    margin: 0;
    list-style: none;
}

.social-links a {
    text-decoration: none;
}

@media (min-width: 768px) {
    .footer-content {
        flex-direction: row;
        justify-content: space-between;
    }
}

/* --- Cookie Banner --- */
#cookie-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2000;
    background-color: var(--pico-card-background-color);
    border-top: 1px solid var(--pico-muted-border-color);
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
}

#cookie-banner .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    text-align: center;
}

#cookie-banner p {
    margin: 0;
    font-size: 0.9rem;
}

#cookie-banner button {
    padding: 0.5rem 1.5rem;
    margin: 0;
}

@media (min-width: 768px) {
    #cookie-banner .container {
        flex-direction: row;
        justify-content: space-between;
        text-align: left;
    }
}

/* --- ЭТАП 7.5: ИСПРАВЛЕНИЯ И ДОРАБОТКИ --- */

/* 1. Уменьшающаяся шапка */
body > header {
    transition: padding 0.3s ease-in-out, background-color 0.3s ease-in-out;
}
body > header.is-scrolled {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    background-color: rgba(255, 255, 255, 0.95); /* Делаем фон менее прозрачным при скролле */
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

/* 2. Исправление "сломанных" кнопок в секции категорий */
.category-meta {
    align-items: stretch; /* Растягиваем элементы по высоте */
}
.category-meta .price-tag,
.category-meta button {
    width: 100%;
    box-sizing: border-box; /* Учитываем padding и border в ширине */
    margin: 0;
}
.category-meta button {
    height: 100%; /* Делаем кнопку такой же высоты, как и блок с ценой */
}
@media (max-width: 767px) {
    .category-meta {
        flex-direction: row; /* На мобильных ставим в ряд */
        gap: 0.5rem;
    }
    .category-meta .price-tag,
    .category-meta button {
        flex: 1; /* Занимают равное пространство */
    }
}


/* 3. Исправление иконки в FAQ */
.faq-accordion summary {
    list-style: none; /* Убирает стандартный маркер > */
}
.faq-accordion summary::-webkit-details-marker {
    display: none; /* И еще раз для Safari */
}

/* 4. Стили для кнопок в Cookie баннере */
.cookie-buttons {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0; /* Предотвращает сжатие кнопок */
}
