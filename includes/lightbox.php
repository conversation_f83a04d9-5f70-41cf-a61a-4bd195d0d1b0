<?php
/**
 * lightbox.php
 *
 * Глобальный компонент для просмотра увеличенных изображений.
 * Управляется через Alpine.js, слушает глобальное событие 'open-lightbox'.
 */
?>
<div
    id="lightbox"
    x-data="{
        open: false,
        imageSrc: '',
        altText: ''
    }"
    x-show="open"
    @open-lightbox.window="
        open = true;
        imageSrc = $event.detail.src;
        altText = $event.detail.alt;
        $nextTick(() => $refs.lightboxImage.focus());
    "
    @keydown.escape.window="open = false"
    x-transition:enter="transition ease-out duration-300"
    x-transition:enter-start="opacity-0"
    x-transition:enter-end="opacity-100"
    x-transition:leave="transition ease-in duration-200"
    x-transition:leave-start="opacity-100"
    x-transition:leave-end="opacity-0"
    class="lightbox-overlay"
    tabindex="-1"
>
    <div class="lightbox-backdrop" @click="open = false"></div>

    <div class="lightbox-content">
        <button class="close-button" @click="open = false" aria-label="Закрыть изображение">×</button>
        
        <div class="image-container">
            <img 
                x-ref="lightboxImage"
                :src="imageSrc" 
                :alt="altText" 
                @click.stop 
            >
        </div>
        
        <div class="alt-text-container" x-show="altText">
            <p x-text="altText"></p>
        </div>
    </div>
</div>
