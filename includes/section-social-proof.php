<?php
/**
 * section-social-proof.php
 *
 * Секция с галереями из социальных сетей.
 * - Демонстрирует активность компании и экспертизу.
 * - Ссылки ведут на внешние ресурсы для стимуляции подписок.
 */
?>
<section id="social-proof">
    <div class="container">
        <hgroup>
            <h2>Мы в социальных сетях</h2>
            <p>Следите за нашими новыми работами, обзорами и советами.</p>
        </hgroup>

        <?php foreach ($social_galleries as $type => $gallery): ?>
            <div class="social-gallery">
                <h3><?= htmlspecialchars($gallery['title']) ?></h3>
                
                <div 
                    class="slider-multi-item"
                    x-data="{
                        currentSlide: 0,
                        totalSlides: <?= count($gallery['items']) ?>,
                        slidesToShow: 1,
                        
                        init() {
                            this.updateSlidesToShow();
                            window.addEventListener('resize', () => this.updateSlidesToShow());
                        },
                        
                        updateSlidesToShow() {
                            if (window.innerWidth >= 1024) {
                                this.slidesToShow = 3;
                            } else if (window.innerWidth >= 768) {
                                this.slidesToShow = 2;
                            } else {
                                this.slidesToShow = 1;
                            }
                        },

                        next() {
                            if (this.currentSlide < this.totalSlides - this.slidesToShow) {
                                this.currentSlide++;
                            }
                        },

                        prev() {
                            if (this.currentSlide > 0) {
                                this.currentSlide--;
                            }
                        }
                    }"
                >
                    <div class="slider-container">
                        <div class="slider-wrapper-flex" :style="`transform: translateX(-${currentSlide * (100 / slidesToShow)}%)`">
                            <?php foreach ($gallery['items'] as $item): ?>
                                <div class="slide-item <?= $type === 'youtube' ? 'aspect-16-9' : 'aspect-9-16' ?>">
                                    <a href="<?= htmlspecialchars($item['link']) ?>" target="_blank" rel="noopener noreferrer">
                                        <img src="<?= htmlspecialchars($item['cover_image']) ?>" alt="Обложка для <?= htmlspecialchars($gallery['title']) ?>">
                                        <div class="play-icon"></div>
                                    </a>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <template x-if="totalSlides > slidesToShow">
                        <div class="slider-nav-external">
                            <button @click="prev()" :disabled="currentSlide === 0" aria-label="Предыдущий"><</button>
                            <button @click="next()" :disabled="currentSlide >= totalSlides - slidesToShow" aria-label="Следующий">></button>
                        </div>
                    </template>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</section>
