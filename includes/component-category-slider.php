<?php
/**
 * component-category-slider.php
 *
 * Переиспользуемый компонент для отображения одной категории товаров.
 * - Принимает данные из переменной $category.
 * - Генерирует адаптивный горизонтальный слайдер.
 * - Отправляет событие для открытия лайтбокса по клику на изображение.
 */

// Проверка на случай, если файл подключили без данных
if (!isset($category) || !is_array($category)) {
    return;
}

$images = $category['images'] ?? [];
$total_slides = count($images);

?>
<section class="category-section">
    <div class="container">
        <div class="category-section-header">
            <hgroup>
                <h2><?= htmlspecialchars($category['title']) ?></h2>
                <p><?= htmlspecialchars($category['description'] ?? '') ?></p>
            </hgroup>
            <div class="category-meta">
                <div class="price-tag">от <?= htmlspecialchars($category['price_from']) ?></div>
                <button 
                    class="outline"
                    @click="$dispatch('open-modal', { title: 'Расчет стоимости: <?= htmlspecialchars($category['title']) ?>' })">
                    Рассчитать под ключ
                </button>
            </div>
        </div>

        <?php if ($total_slides > 0): ?>
        <div 
            class="slider-multi-item"
            x-data="{
                currentSlide: 0,
                totalSlides: <?= $total_slides ?>,
                slidesToShow: 1,
                
                init() {
                    this.updateSlidesToShow();
                    window.addEventListener('resize', () => this.updateSlidesToShow());
                },
                
                updateSlidesToShow() {
                    if (window.innerWidth >= 1200) {
                        this.slidesToShow = 4;
                    } else if (window.innerWidth >= 992) {
                        this.slidesToShow = 3;
                    } else if (window.innerWidth >= 768) {
                        this.slidesToShow = 2;
                    } else {
                        this.slidesToShow = 1;
                    }
                },

                next() {
                    if (this.currentSlide < this.totalSlides - this.slidesToShow) {
                        this.currentSlide++;
                    } else {
                        this.currentSlide = 0; // Возврат к началу
                    }
                },

                prev() {
                    if (this.currentSlide > 0) {
                        this.currentSlide--;
                    } else {
                        this.currentSlide = this.totalSlides - this.slidesToShow; // Переход в конец
                    }
                }
            }"
        >
            <div class="slider-container">
                <div class="slider-wrapper-flex" :style="`transform: translateX(-${currentSlide * (100 / slidesToShow)}%)`">
                    <?php foreach ($images as $image): ?>
                        <div class="slide-item aspect-4-3">
                            <a 
                                href="<?= htmlspecialchars($image['full']) ?>" 
                                @click.prevent="$dispatch('open-lightbox', { 
                                    src: '<?= htmlspecialchars($image['full']) ?>', 
                                    alt: '<?= htmlspecialchars($image['alt']) ?>' 
                                })"
                                class="slide-link"
                            >
                                <img 
                                    src="<?= htmlspecialchars($image['thumb']) ?>" 
                                    alt="<?= htmlspecialchars($image['alt']) ?>"
                                    loading="lazy"
                                >
                                <div class="zoom-icon"></div>
                            </a>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <template x-if="totalSlides > slidesToShow">
                <div class="slider-nav-external">
                    <button @click="prev()" aria-label="Предыдущие работы"><</button>
                    <button @click="next()" aria-label="Следующие работы">></button>
                </div>
            </template>
        </div>
        <?php endif; ?>
    </div>
</section>
