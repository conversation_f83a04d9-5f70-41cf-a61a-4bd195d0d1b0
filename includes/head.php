<?php
/**
 * head.php
 *
 * Формирует секцию <head> страницы.
 * - Подключает стили и скрипты.
 * - Выводит мета-теги для SEO.
 * - Реализует логику выбора темы.
 * - Генерирует JSON-LD микроразметку.
 */

// --- Логика выбора темы для A/B теста ---
$default_theme = 'light';
$allowed_themes = ['light', 'dark'];
$current_theme = $default_theme;

if (isset($_GET['theme']) && in_array($_GET['theme'], $allowed_themes)) {
    $current_theme = $_GET['theme'];
}

// --- Формирование URL для Open Graph и Canonical ---
$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? "https://" : "http://";
$host = $_SERVER['HTTP_HOST'];
$base_url = $protocol . $host;

?>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- Основные SEO-теги -->
    <title><?= htmlspecialchars($seo['title']) ?></title>
    <meta name="description" content="<?= htmlspecialchars($seo['description']) ?>">
    <meta name="keywords" content="<?= htmlspecialchars($seo['keywords']) ?>">
    <link rel="canonical" href="<?= $base_url ?>">

    <!-- Open Graph для соцсетей -->
    <meta property="og:title" content="<?= htmlspecialchars($seo['og_title']) ?>">
    <meta property="og:description" content="<?= htmlspecialchars($seo['og_description']) ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?= $base_url ?>">
    <meta property="og:image" content="<?= $base_url . htmlspecialchars($seo['og_image_url']) ?>">
    <meta property="og:site_name" content="<?= htmlspecialchars($contacts['company_name']) ?>">
    <meta property="og:locale" content="ru_RU">

    <!-- Подключение библиотек через CDN -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@picocss/pico@2/css/pico.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3/dist/cdn.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/@alpine-collective/toolkit@1.0.0/dist/cdn.min.js"></script> <!-- ПЛАГИН ДЛЯ СВАЙПА -->

    <!-- Подключение кастомных стилей -->
    <link rel="stylesheet" href="/assets/css/style.css">
    <link rel="stylesheet" href="/assets/css/theme-<?= $current_theme ?>.css">

    <!-- JSON-LD Микроразметка (Schema.org) -->
    <script type="application/ld+json">
    <?= json_encode([
        '@context' => 'https://schema.org',
        '@type' => 'HomeAndConstructionBusiness',
        'name' => $contacts['company_name'],
        'description' => $seo['description'],
        'image' => $base_url . htmlspecialchars($schema_data['logo_url']),
        'logo' => $base_url . htmlspecialchars($schema_data['logo_url']),
        'url' => $base_url,
        'telephone' => $contacts['phone']['link'],
        'email' => $contacts['email'],
        'address' => [
            '@type' => 'PostalAddress',
            'streetAddress' => $contacts['address']['street'],
            'addressLocality' => $contacts['address']['city'],
            'addressCountry' => 'RU'
        ],
        'geo' => [
            '@type' => 'GeoCoordinates',
            'latitude' => $schema_data['geo']['latitude'],
            'longitude' => $schema_data['geo']['longitude']
        ],
        'openingHoursSpecification' => [
            ['@type' => 'OpeningHoursSpecification', 'dayOfWeek' => ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'], 'opens' => '10:00', 'closes' => '20:00'],
            ['@type' => 'OpeningHoursSpecification', 'dayOfWeek' => ['Saturday', 'Sunday'], 'opens' => '11:00', 'closes' => '18:00']
        ]
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT); ?>
    </script>
</head>
